const { ObjectId } = require('mongodb');
const { findOne, find, insertOne, updateOne, deleteOne, aggregate } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

// Main handler function
exports.handler = async (event, context) => {
  // Add CORS headers for all requests
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const { httpMethod, path } = event;
  const pathSegments = path.split('/').filter(Boolean);
  const action = pathSegments[pathSegments.length - 1];

  console.log(`Messages API: ${httpMethod} ${path} - Action: ${action}`);
  console.log('Environment variables check:', {
    NODE_ENV: process.env.NODE_ENV,
    MONGODB_URI_EXISTS: !!process.env.MONGODB_URI,
    DB_NAME: process.env.DB_NAME
  });

  try {
    let result;

    switch (httpMethod) {
      case 'GET':
        if (action === 'conversations') {
          result = await exports.getConversations(event);
        } else if (action === 'messages') {
          result = await exports.getMessages(event);
        } else if (action === 'new-messages') {
          result = await exports.getNewMessages(event);
        } else if (action === 'test') {
          result = await exports.testConnection(event);
        } else {
          result = error('Invalid GET endpoint', 404);
        }
        break;

      case 'POST':
        if (action === 'create-conversation') {
          result = await exports.getOrCreateConversation(event);
        } else if (action === 'send') {
          result = await exports.sendMessage(event);
        } else if (action === 'user-status') {
          result = await exports.getUserStatus(event);
        } else if (action === 'update-status') {
          result = await exports.updateUserStatus(event);
        } else if (action === 'sync') {
          result = await exports.syncMessages(event);
        } else if (action === 'mark-read') {
          result = await exports.markMessagesAsRead(event);
        } else if (action === 'typing') {
          result = await exports.sendTypingIndicator(event);
        } else {
          result = error('Invalid POST endpoint', 404);
        }
        break;

      case 'PUT':
        if (action === 'edit') {
          result = await exports.editMessage(event);
        } else {
          result = error('Invalid PUT endpoint', 404);
        }
        break;

      case 'DELETE':
        if (action === 'delete') {
          result = await exports.deleteMessage(event);
        } else {
          result = error('Invalid DELETE endpoint', 404);
        }
        break;

      default:
        result = error('Method not allowed', 405);
    }

    // Add CORS headers to the result
    return {
      ...result,
      headers: {
        ...headers,
        ...result.headers
      }
    };

  } catch (err) {
    console.error('Messages handler error:', err);
    console.error('Error stack:', err.stack);
    console.error('Event details:', {
      httpMethod: event.httpMethod,
      path: event.path,
      headers: event.headers,
      queryStringParameters: event.queryStringParameters
    });

    // Return more specific error information
    const statusCode = err.statusCode || 500;
    const errorMessage = err.message || 'Internal server error';

    return {
      statusCode,
      headers,
      body: JSON.stringify({
        success: false,
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? err.stack : undefined,
        timestamp: new Date().toISOString()
      })
    };
  }
};

// Create or get conversation
exports.getOrCreateConversation = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { participantId } = JSON.parse(event.body);

  // Validate input
  if (!participantId) {
    return error('Participant ID is required');
  }

  // Check if participant exists
  const participant = await findOne('users', { _id: new ObjectId(participantId) });
  if (!participant) {
    return error('Participant not found', 404);
  }

  // Check if conversation already exists
  const existingConversation = await findOne('conversations', {
    participants: {
      $all: [new ObjectId(userId), new ObjectId(participantId)],
      $size: 2
    }
  });

  if (existingConversation) {
    // Return existing conversation
    return success({ conversation: existingConversation });
  }

  // Create new conversation
  const conversation = {
    participants: [new ObjectId(userId), new ObjectId(participantId)],
    lastMessage: null,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Insert conversation into database
  const result = await insertOne('conversations', conversation);

  // Get the inserted conversation
  const newConversation = await findOne('conversations', { _id: result.insertedId });

  // Return the new conversation
  return success({ conversation: newConversation });
}));

// Get conversations
exports.getConversations = handleAsync(requireAuth(async (event) => {
  try {
    // Get user ID from token
    const userId = event.user._id;
    console.log(`Getting conversations for user: ${userId}`);

    // Validate user ID
    if (!userId) {
      console.error('User ID is missing from token');
      return error('Invalid user authentication', 401);
    }

    // Test database connection first
    try {
      const { checkDatabaseStatus } = require('./utils/db');
      const dbStatus = await checkDatabaseStatus();
      if (!dbStatus.connected) {
        console.error('Database not connected:', dbStatus);
        return error('Database connection failed. Please try again in a moment.', 503);
      }
    } catch (dbError) {
      console.error('Database status check failed:', dbError);
      return error('Database service unavailable. Please try again in a moment.', 503);
    }

    // Get conversations with participant details and unread counts
    const conversations = await aggregate('conversations', [
      { $match: { participants: new ObjectId(userId) } },
      { $lookup: {
          from: 'users',
          localField: 'participants',
          foreignField: '_id',
          as: 'participantDetails'
        }
      },
      { $lookup: {
          from: 'messages',
          let: { conversationId: '$_id' },
          pipeline: [
            { $match: {
                $expr: {
                  $and: [
                    { $eq: ['$conversationId', '$$conversationId'] },
                    { $ne: ['$sender', new ObjectId(userId)] },
                    { $eq: ['$isRead', { $ifNull: ['$isRead', false] }] }
                  ]
                }
              }
            },
            { $count: 'unreadCount' }
          ],
          as: 'unreadMessages'
        }
      },
      { $project: {
          _id: 1,
          participants: 1,
          lastMessage: 1,
          createdAt: 1,
          updatedAt: 1,
          unreadCount: { $ifNull: [{ $arrayElemAt: ['$unreadMessages.unreadCount', 0] }, 0] },
          participantDetails: {
            $filter: {
              input: '$participantDetails',
              as: 'participant',
              cond: { $ne: ['$$participant._id', new ObjectId(userId)] }
            }
          }
        }
      },
      { $unwind: { path: '$participantDetails', preserveNullAndEmptyArrays: true } },
      { $project: {
          _id: 1,
          lastMessage: 1,
          createdAt: 1,
          updatedAt: 1,
          unreadCount: 1,
          participant: {
            _id: '$participantDetails._id',
            username: '$participantDetails.username',
            displayName: '$participantDetails.displayName',
            profilePicture: '$participantDetails.profilePicture',
            isPremium: '$participantDetails.isPremium',
            isVerified: '$participantDetails.isVerified'
          }
        }
      },
      { $sort: { updatedAt: -1 } }
    ]);

    console.log(`Found ${conversations.length} conversations for user ${userId}`);

    // Return conversations with proper structure
    return success({
      conversations,
      count: conversations.length,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error('Error in getConversations:', err);
    return error(`Failed to fetch conversations: ${err.message}`, 500);
  }
}));

// Send message
exports.sendMessage = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { conversationId, content } = JSON.parse(event.body);

  // Validate input
  if (!conversationId || !content) {
    return error('Conversation ID and content are required');
  }

  // Check if conversation exists
  const conversation = await findOne('conversations', { _id: new ObjectId(conversationId) });
  if (!conversation) {
    return error('Conversation not found', 404);
  }

  // Check if user is a participant
  if (!conversation.participants.some(p => p.toString() === userId)) {
    return error('You are not a participant in this conversation', 403);
  }

  // Create message
  const message = {
    conversationId: new ObjectId(conversationId),
    sender: new ObjectId(userId),
    content,
    editHistory: [],
    isDeleted: false,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Insert message into database
  const result = await insertOne('messages', message);

  // Update conversation with last message
  await updateOne(
    'conversations',
    { _id: new ObjectId(conversationId) },
    {
      $set: {
        lastMessage: {
          _id: result.insertedId,
          sender: new ObjectId(userId),
          content,
          createdAt: new Date()
        },
        updatedAt: new Date()
      }
    }
  );

  // Get the inserted message
  const newMessage = await findOne('messages', { _id: result.insertedId });

  // Return the new message
  return success({ message: newMessage });
}));

// Get messages
exports.getMessages = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Get conversation ID from query parameters
  const conversationId = event.queryStringParameters.conversationId;

  // Validate input
  if (!conversationId) {
    return error('Conversation ID is required');
  }

  // Check if conversation exists
  const conversation = await findOne('conversations', { _id: new ObjectId(conversationId) });
  if (!conversation) {
    return error('Conversation not found', 404);
  }

  // Check if user is a participant
  if (!conversation.participants.some(p => p.toString() === userId)) {
    return error('You are not a participant in this conversation', 403);
  }

  // Get pagination parameters
  const page = parseInt(event.queryStringParameters?.page) || 1;
  const limit = parseInt(event.queryStringParameters?.limit) || 20;
  const skip = (page - 1) * limit;

  // Get messages
  const messages = await find(
    'messages',
    { conversationId: new ObjectId(conversationId) },
    {
      sort: { createdAt: -1 },
      skip,
      limit
    }
  );

  // Return messages
  return success({ messages, page, limit });
}));

// Edit message
exports.editMessage = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { messageId, content } = JSON.parse(event.body);

  // Validate input
  if (!messageId || !content) {
    return error('Message ID and content are required');
  }

  // Check if message exists
  const message = await findOne('messages', { _id: new ObjectId(messageId) });
  if (!message) {
    return error('Message not found', 404);
  }

  // Check if user is the sender
  if (message.sender.toString() !== userId) {
    return error('You can only edit your own messages', 403);
  }

  // Add current content to edit history
  const editHistory = [...message.editHistory, {
    content: message.content,
    editedAt: new Date()
  }];

  // Update message
  await updateOne(
    'messages',
    { _id: new ObjectId(messageId) },
    {
      $set: {
        content,
        editHistory,
        updatedAt: new Date()
      }
    }
  );

  // Return success
  return success({ message: 'Message updated successfully' });
}));

// Delete message
exports.deleteMessage = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { messageId } = JSON.parse(event.body);

  // Validate input
  if (!messageId) {
    return error('Message ID is required');
  }

  // Check if message exists
  const message = await findOne('messages', { _id: new ObjectId(messageId) });
  if (!message) {
    return error('Message not found', 404);
  }

  // Check if user is the sender
  if (message.sender.toString() !== userId) {
    return error('You can only delete your own messages', 403);
  }

  // Soft delete message
  await updateOne(
    'messages',
    { _id: new ObjectId(messageId) },
    {
      $set: {
        content: 'This message has been deleted',
        isDeleted: true,
        updatedAt: new Date()
      }
    }
  );

  // Return success
  return success({ message: 'Message deleted successfully' });
}));

// Get new messages since a specific time
exports.getNewMessages = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Get conversation ID and timestamp from query parameters
  const conversationId = event.queryStringParameters.conversationId;
  const since = event.queryStringParameters.since;

  // Validate input
  if (!conversationId || !since) {
    return error('Conversation ID and since timestamp are required');
  }

  // Check if conversation exists
  const conversation = await findOne('conversations', { _id: new ObjectId(conversationId) });
  if (!conversation) {
    return error('Conversation not found', 404);
  }

  // Check if user is a participant
  if (!conversation.participants.some(p => p.toString() === userId)) {
    return error('You are not a participant in this conversation', 403);
  }

  // Get messages since the specified timestamp
  const messages = await find(
    'messages',
    {
      conversationId: new ObjectId(conversationId),
      createdAt: { $gt: new Date(since) }
    },
    {
      sort: { createdAt: 1 }
    }
  );

  // Return messages
  return success({ messages });
}));

// Get user status
exports.getUserStatus = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { userIds } = JSON.parse(event.body);

  // Validate input
  if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
    return error('User IDs are required');
  }

  // Convert user IDs to ObjectId
  const objectIds = userIds.map(id => new ObjectId(id));

  // Get user statuses
  const users = await find('users', { _id: { $in: objectIds } });

  // Create status map
  const statuses = {};

  for (const user of users) {
    // Check if user has a recent activity timestamp (within last 5 minutes)
    const isOnline = user.lastActivityAt &&
      (new Date() - new Date(user.lastActivityAt)) < 5 * 60 * 1000;

    statuses[user._id.toString()] = isOnline ? 'online' : 'offline';
  }

  // Return statuses
  return success({ statuses });
}));

// Update user status
exports.updateUserStatus = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { status } = JSON.parse(event.body);

  // Validate input
  if (!status) {
    return error('Status is required');
  }

  // Update user status
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    { $set: { lastActivityAt: new Date() } }
  );

  // Return success
  return success({ message: 'Status updated successfully' });
}));

// Offline sync for messages
exports.syncMessages = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { messages: pendingMessages } = JSON.parse(event.body);

  // Validate input
  if (!pendingMessages || !Array.isArray(pendingMessages) || pendingMessages.length === 0) {
    return error('Pending messages are required');
  }

  // Process each pending message
  const results = [];

  for (const pendingMessage of pendingMessages) {
    try {
      // Validate message
      if (!pendingMessage.conversationId || !pendingMessage.content) {
        results.push({
          clientId: pendingMessage.clientId,
          success: false,
          error: 'Invalid message format'
        });
        continue;
      }

      // Check if conversation exists
      const conversation = await findOne('conversations', { _id: new ObjectId(pendingMessage.conversationId) });
      if (!conversation) {
        results.push({
          clientId: pendingMessage.clientId,
          success: false,
          error: 'Conversation not found'
        });
        continue;
      }

      // Check if user is a participant
      if (!conversation.participants.some(p => p.toString() === userId)) {
        results.push({
          clientId: pendingMessage.clientId,
          success: false,
          error: 'Not a participant in this conversation'
        });
        continue;
      }

      // Create message
      const message = {
        conversationId: new ObjectId(pendingMessage.conversationId),
        sender: new ObjectId(userId),
        content: pendingMessage.content,
        editHistory: [],
        isDeleted: false,
        createdAt: new Date(pendingMessage.timestamp || Date.now()),
        updatedAt: new Date(pendingMessage.timestamp || Date.now())
      };

      // Insert message into database
      const result = await insertOne('messages', message);

      // Update conversation with last message
      await updateOne(
        'conversations',
        { _id: new ObjectId(pendingMessage.conversationId) },
        {
          $set: {
            lastMessage: {
              _id: result.insertedId,
              sender: new ObjectId(userId),
              content: pendingMessage.content,
              createdAt: message.createdAt
            },
            updatedAt: new Date()
          }
        }
      );

      // Add result
      results.push({
        clientId: pendingMessage.clientId,
        success: true,
        messageId: result.insertedId.toString()
      });
    } catch (err) {
      console.error('Error processing pending message:', err);

      results.push({
        clientId: pendingMessage.clientId,
        success: false,
        error: 'Server error'
      });
    }
  }

  // Return results
  return success({ results });
}));

// Mark messages as read
exports.markMessagesAsRead = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { conversationId, messageIds } = JSON.parse(event.body);

  // Validate input
  if (!conversationId) {
    return error('Conversation ID is required');
  }

  // Check if conversation exists
  const conversation = await findOne('conversations', { _id: new ObjectId(conversationId) });
  if (!conversation) {
    return error('Conversation not found', 404);
  }

  // Check if user is a participant
  if (!conversation.participants.some(p => p.toString() === userId)) {
    return error('You are not a participant in this conversation', 403);
  }

  // Mark messages as read
  const filter = {
    conversationId: new ObjectId(conversationId),
    sender: { $ne: new ObjectId(userId) }
  };

  // If specific message IDs provided, filter by them
  if (messageIds && Array.isArray(messageIds) && messageIds.length > 0) {
    filter._id = { $in: messageIds.map(id => new ObjectId(id)) };
  }

  await updateOne(
    'messages',
    filter,
    { $set: { isRead: true, readAt: new Date() } },
    { multi: true }
  );

  return success({ message: 'Messages marked as read' });
}));

// Get typing status
exports.getTypingStatus = handleAsync(requireAuth(async (event) => {
  // Get conversation ID from query parameters
  const conversationId = event.queryStringParameters.conversationId;

  // Validate input
  if (!conversationId) {
    return error('Conversation ID is required');
  }

  // For now, return empty typing status since we don't have persistent typing storage
  // In a real WebSocket implementation, this would be managed in memory
  return success({ typingUsers: [] });
}));

// Send typing indicator (placeholder for WebSocket functionality)
exports.sendTypingIndicator = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { conversationId, isTyping } = JSON.parse(event.body);

  // Validate input
  if (!conversationId) {
    return error('Conversation ID is required');
  }

  // In a real WebSocket implementation, this would broadcast to other participants
  // For now, just return success
  return success({
    message: `Typing indicator ${isTyping ? 'started' : 'stopped'}`,
    userId,
    conversationId,
    isTyping
  });
}));

// Create group conversation
exports.createGroup = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { name, description, members } = JSON.parse(event.body);

  // Validate input
  if (!name || !members || !Array.isArray(members) || members.length === 0) {
    return error('Group name and members are required');
  }

  // Validate group name length
  if (name.trim().length === 0 || name.trim().length > 50) {
    return error('Group name must be between 1 and 50 characters');
  }

  // Validate description length if provided
  if (description && description.length > 200) {
    return error('Group description must be less than 200 characters');
  }

  // Convert member IDs to ObjectId and add current user
  const memberObjectIds = members.map(id => new ObjectId(id));
  const allParticipants = [new ObjectId(userId), ...memberObjectIds];

  // Remove duplicates
  const uniqueParticipants = [...new Set(allParticipants.map(id => id.toString()))].map(id => new ObjectId(id));

  // Validate that all members exist
  const existingUsers = await find('users', { _id: { $in: uniqueParticipants } });
  if (existingUsers.length !== uniqueParticipants.length) {
    return error('One or more members not found', 404);
  }

  // Create group conversation
  const conversation = {
    participants: uniqueParticipants,
    isGroup: true,
    groupName: name.trim(),
    groupDescription: description?.trim() || null,
    groupAdmin: new ObjectId(userId),
    lastMessage: null,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Insert conversation into database
  const result = await insertOne('conversations', conversation);

  // Create initial system message
  const systemMessage = {
    conversationId: result.insertedId,
    sender: new ObjectId(userId),
    content: `${event.user.username} created the group "${name.trim()}"`,
    isSystemMessage: true,
    editHistory: [],
    isDeleted: false,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Insert system message
  const messageResult = await insertOne('messages', systemMessage);

  // Update conversation with system message as last message
  await updateOne(
    'conversations',
    { _id: result.insertedId },
    {
      $set: {
        lastMessage: {
          _id: messageResult.insertedId,
          sender: new ObjectId(userId),
          content: systemMessage.content,
          createdAt: systemMessage.createdAt
        },
        updatedAt: new Date()
      }
    }
  );

  // Get the created conversation with participant details
  const newConversation = await aggregate('conversations', [
    { $match: { _id: result.insertedId } },
    { $lookup: {
        from: 'users',
        localField: 'participants',
        foreignField: '_id',
        as: 'participantDetails'
      }
    },
    { $project: {
        _id: 1,
        participants: 1,
        isGroup: 1,
        groupName: 1,
        groupDescription: 1,
        groupAdmin: 1,
        lastMessage: 1,
        createdAt: 1,
        updatedAt: 1,
        participantDetails: {
          $map: {
            input: '$participantDetails',
            as: 'participant',
            in: {
              _id: '$$participant._id',
              username: '$$participant.username',
              displayName: '$$participant.displayName',
              profilePicture: '$$participant.profilePicture',
              isPremium: '$$participant.isPremium'
            }
          }
        }
      }
    }
  ]);

  // Return the new conversation
  return success({
    conversationId: result.insertedId.toString(),
    conversation: newConversation[0]
  });
}));

// Test connection endpoint
exports.testConnection = handleAsync(async (event) => {
  try {
    const { checkDatabaseStatus } = require('./utils/db');
    const dbStatus = await checkDatabaseStatus();

    return success({
      message: 'Messages API is working',
      timestamp: new Date().toISOString(),
      database: dbStatus,
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        MONGODB_URI_EXISTS: !!process.env.MONGODB_URI,
        DB_NAME: process.env.DB_NAME
      }
    });
  } catch (err) {
    console.error('Test connection error:', err);
    return error(`Test failed: ${err.message}`, 500);
  }
});
