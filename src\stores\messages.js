import { defineStore } from 'pinia';
import { ref, computed, onUnmounted } from 'vue';
import { useAuthStore } from './auth';
import webSocketService from '../services/websocket';
import encryptionService from '../services/encryption';

export const useMessagesStore = defineStore('messages', () => {
  // State
  const conversations = ref([]);
  const currentConversation = ref(null);
  const messages = ref([]);
  const loading = ref(false);
  const error = ref(null);
  const page = ref(1);
  const hasMore = ref(true);

  // Real-time polling
  const pollingInterval = ref(null);
  const pollingFrequency = 5000; // 5 seconds
  const lastMessageTimestamp = ref(null);

  // User status
  const userStatuses = ref({});
  const statusPollingInterval = ref(null);

  // Offline sync
  const pendingMessages = ref([]);
  const isOnline = ref(true); // Default to online, will be updated by network checks
  const syncInProgress = ref(false);

  // Real-time features
  const typingUsers = ref({});
  const messageDeliveryStatus = ref({});
  const messageReadStatus = ref({});
  const isWebSocketConnected = ref(false);

  // Typing indicator
  const typingTimeout = ref(null);
  const typingIndicatorDelay = 1000; // 1 second

  // Getters
  const allConversations = computed(() => conversations.value);
  const currentMessages = computed(() => messages.value);
  const conversationDetails = computed(() => currentConversation.value);

  // Get user status by ID
  const getUserStatus = computed(() => (userId) => {
    return userStatuses.value[userId] || 'offline';
  });

  // Check if a user is online
  const isUserOnline = computed(() => (userId) => {
    return userStatuses.value[userId] === 'online';
  });

  // Actions
  async function fetchConversations() {
    const authStore = useAuthStore();
    if (!authStore.token) {
      console.warn('No auth token available for fetching conversations');
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      console.log('Fetching conversations...');

      const response = await fetch('/.netlify/functions/messages/conversations', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Conversations response status:', response.status);

      if (!response.ok) {
        // Handle different error status codes
        if (response.status === 503) {
          throw new Error('Service temporarily unavailable. Please try again in a moment.');
        } else if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        } else if (response.status === 500) {
          throw new Error('Server error. Please try again later.');
        } else {
          throw new Error(`HTTP ${response.status}: Failed to fetch conversations`);
        }
      }

      const data = await response.json();
      console.log('Conversations data:', data);

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch conversations');
      }

      // Update conversations
      conversations.value = data.data?.conversations || [];
      console.log(`Loaded ${conversations.value.length} conversations`);

      return conversations.value;
    } catch (err) {
      console.error('Error fetching conversations:', err);

      // Set user-friendly error messages
      if (err.message.includes('fetch')) {
        error.value = 'Network error. Please check your connection and try again.';
      } else {
        error.value = err.message;
      }

      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchMessages(conversationId, refresh = false) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    loading.value = true;
    error.value = null;

    try {
      // Reset page if refreshing
      if (refresh) {
        page.value = 1;
        messages.value = [];
        hasMore.value = true;
      }

      // Don't fetch if no more messages
      if (!hasMore.value && !refresh) {
        loading.value = false;
        return [];
      }

      const response = await fetch(`/.netlify/functions/messages/messages?conversationId=${conversationId}&page=${page.value}&limit=20`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch messages');
      }

      // Update messages
      if (refresh) {
        messages.value = data.data.messages;
      } else {
        messages.value = [...data.data.messages, ...messages.value];
      }

      // Check if there are more messages
      hasMore.value = data.data.messages.length === 20;

      // Increment page for next fetch
      if (hasMore.value) {
        page.value++;
      }

      return data.data.messages;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function getOrCreateConversation(participantId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/messages/create-conversation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ participantId })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create conversation');
      }

      // Set current conversation
      currentConversation.value = data.data.conversation;

      // Reset messages
      messages.value = [];
      page.value = 1;
      hasMore.value = true;

      // Fetch messages for the conversation
      await fetchMessages(data.data.conversation._id, true);

      // Refresh conversations list
      await fetchConversations();

      return data.data.conversation;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  // Initialize offline sync
  function initOfflineSync() {
    // Check initial online status more reliably
    isOnline.value = navigator.onLine !== false; // Default to true unless explicitly false

    // Load pending messages from local storage
    loadPendingMessages();

    // Set up online/offline event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Sync pending messages if online
    if (isOnline.value) {
      syncPendingMessages();
    }

    // Clean up on component unmount
    onUnmounted(() => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    });
  }

  // Handle going online
  async function handleOnline() {
    isOnline.value = true;
    console.log('Connection restored. Syncing pending messages...');

    // Sync pending messages
    await syncPendingMessages();
  }

  // Handle going offline
  function handleOffline() {
    isOnline.value = false;
    console.log('Connection lost. Messages will be queued for later delivery.');
  }

  // Save pending messages to local storage
  function savePendingMessages() {
    try {
      localStorage.setItem('pendingMessages', JSON.stringify(pendingMessages.value));
    } catch (err) {
      console.error('Failed to save pending messages to local storage:', err);
    }
  }

  // Load pending messages from local storage
  function loadPendingMessages() {
    try {
      const stored = localStorage.getItem('pendingMessages');
      if (stored) {
        pendingMessages.value = JSON.parse(stored);
      }
    } catch (err) {
      console.error('Failed to load pending messages from local storage:', err);
    }
  }

  // Sync pending messages with the server
  async function syncPendingMessages() {
    if (syncInProgress.value || pendingMessages.value.length === 0) return;

    const authStore = useAuthStore();
    if (!authStore.token) return;

    syncInProgress.value = true;

    try {
      // Get pending messages that need to be synced
      const messagesToSync = pendingMessages.value.filter(
        msg => msg.status === 'pending' || msg.status === 'failed'
      );

      if (messagesToSync.length === 0) {
        syncInProgress.value = false;
        return;
      }

      // Send messages to server
      const response = await fetch('/.netlify/functions/messages/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ messages: messagesToSync })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to sync messages');
      }

      // Process results
      for (const result of data.data.results) {
        const pendingIndex = pendingMessages.value.findIndex(
          msg => msg.clientId === result.clientId
        );

        if (pendingIndex !== -1) {
          if (result.success) {
            // Remove from pending messages
            pendingMessages.value.splice(pendingIndex, 1);

            // Update optimistic message in UI
            const messageIndex = messages.value.findIndex(
              msg => msg._id === result.clientId
            );

            if (messageIndex !== -1) {
              messages.value[messageIndex]._id = result.messageId;
              messages.value[messageIndex].isPending = false;
            }
          } else {
            // Mark as failed
            pendingMessages.value[pendingIndex].status = 'failed';
            pendingMessages.value[pendingIndex].error = result.error;
          }
        }
      }

      // Save updated pending messages
      savePendingMessages();
    } catch (err) {
      console.error('Error syncing pending messages:', err);
    } finally {
      syncInProgress.value = false;
    }
  }

  async function sendMessage(content) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    if (!currentConversation.value) throw new Error('No active conversation');
    if (!content || content.trim() === '') throw new Error('Message content is required');

    // Validate and sanitize content
    encryptionService.validateMessageContent(content);
    const sanitizedContent = encryptionService.sanitizeContent(content);

    const conversationId = currentConversation.value._id;

    // Encrypt message content
    const encryptedContent = encryptionService.encryptForConversation(
      sanitizedContent,
      currentConversation.value,
      authStore.user._id
    );

    // Stop typing indicator
    stopTypingIndicator();

    // Try WebSocket first if connected
    if (isWebSocketConnected.value) {
      webSocketService.sendMessage(conversationId, encryptedContent);

      // Add optimistic message to UI
      const optimisticMessage = {
        _id: `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        conversationId,
        sender: authStore.user,
        content: sanitizedContent, // Show decrypted content in UI
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'sending'
      };

      messages.value = [...messages.value, optimisticMessage];
      updateConversationWithNewMessage(conversationId, optimisticMessage);

      return optimisticMessage;
    }

    // Check if we're online (use navigator.onLine as primary check)
    const isActuallyOnline = navigator.onLine !== false;

    if (!isActuallyOnline) {
      console.log('Device is offline, storing message for later sync');

      // Store message for later sync
      const pendingMessage = {
        clientId: `pending-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        conversationId,
        content: encryptedContent,
        timestamp: new Date().toISOString(),
        status: 'pending'
      };

      // Add to pending messages
      pendingMessages.value.push(pendingMessage);

      // Save to local storage
      savePendingMessages();

      // Add optimistic message to UI
      const optimisticMessage = {
        _id: pendingMessage.clientId,
        conversationId,
        sender: authStore.user,
        content: sanitizedContent, // Show decrypted content in UI
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isPending: true
      };

      // Add message to the messages array
      messages.value = [...messages.value, optimisticMessage];
      updateConversationWithNewMessage(conversationId, optimisticMessage);

      return optimisticMessage;
    }

    // We're online, send message normally
    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/messages/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          conversationId,
          content: encryptedContent
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send message');
      }

      // Decrypt message content for UI
      const decryptedMessage = {
        ...data.data.message,
        content: encryptionService.decryptForConversation(
          data.data.message.content,
          currentConversation.value,
          authStore.user._id
        )
      };

      // Add new message to the messages array
      messages.value = [...messages.value, decryptedMessage];
      updateConversationWithNewMessage(conversationId, decryptedMessage);

      return decryptedMessage;
    } catch (err) {
      error.value = err.message;

      // If we failed to send, add to pending messages
      const pendingMessage = {
        clientId: `pending-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        conversationId,
        content,
        timestamp: new Date().toISOString(),
        status: 'failed'
      };

      // Add to pending messages
      pendingMessages.value.push(pendingMessage);

      // Save to local storage
      savePendingMessages();

      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function editMessage(messageId, content) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/messages/edit', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ messageId, content })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to edit message');
      }

      // Update message in the messages array
      const messageIndex = messages.value.findIndex(m => m._id === messageId);
      if (messageIndex !== -1) {
        messages.value[messageIndex].content = content;
        messages.value[messageIndex].updatedAt = new Date();
      }

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function deleteMessage(messageId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/messages/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ messageId })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete message');
      }

      // Update message in the messages array
      const messageIndex = messages.value.findIndex(m => m._id === messageId);
      if (messageIndex !== -1) {
        messages.value[messageIndex].content = 'This message has been deleted';
        messages.value[messageIndex].isDeleted = true;
        messages.value[messageIndex].updatedAt = new Date();
      }

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  function setCurrentConversation(conversation) {
    // Stop any existing polling
    stopRealtimePolling();

    currentConversation.value = conversation;
    messages.value = [];
    page.value = 1;
    hasMore.value = true;

    if (conversation) {
      fetchMessages(conversation._id, true).then(() => {
        // Start real-time polling for new messages
        startRealtimePolling(conversation._id);

        // Start polling for user status
        if (conversation.participant && conversation.participant._id) {
          startStatusPolling([conversation.participant._id]);
        }
      });
    }
  }

  // Start real-time polling for new messages
  function startRealtimePolling(conversationId) {
    // Stop any existing polling
    stopRealtimePolling();

    // Set the last message timestamp
    if (messages.value.length > 0) {
      const latestMessage = [...messages.value].sort((a, b) =>
        new Date(b.createdAt) - new Date(a.createdAt)
      )[0];

      lastMessageTimestamp.value = new Date(latestMessage.createdAt).toISOString();
    } else {
      lastMessageTimestamp.value = new Date().toISOString();
    }

    // Start polling
    pollingInterval.value = setInterval(async () => {
      try {
        await pollNewMessages(conversationId);
      } catch (err) {
        console.error('Error polling for new messages:', err);
      }
    }, pollingFrequency);

    // Clean up on component unmount
    onUnmounted(() => {
      stopRealtimePolling();
    });
  }

  // Stop real-time polling
  function stopRealtimePolling() {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
  }

  // Poll for new messages
  async function pollNewMessages(conversationId) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      const response = await fetch(`/.netlify/functions/messages/new-messages?conversationId=${conversationId}&since=${lastMessageTimestamp.value}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to poll for new messages');
      }

      // If there are new messages, add them to the messages array
      if (data.data.messages && data.data.messages.length > 0) {
        // Add new messages
        messages.value = [...messages.value, ...data.data.messages];

        // Update last message timestamp
        const latestMessage = [...data.data.messages].sort((a, b) =>
          new Date(b.createdAt) - new Date(a.createdAt)
        )[0];

        lastMessageTimestamp.value = new Date(latestMessage.createdAt).toISOString();

        // Update conversation in the conversations array
        const conversationIndex = conversations.value.findIndex(c => c._id === conversationId);
        if (conversationIndex !== -1 && latestMessage) {
          conversations.value[conversationIndex].lastMessage = {
            _id: latestMessage._id,
            sender: latestMessage.sender,
            content: latestMessage.content,
            createdAt: latestMessage.createdAt
          };

          // Move conversation to the top of the list
          const conversation = conversations.value.splice(conversationIndex, 1)[0];
          conversations.value.unshift(conversation);
        }
      }
    } catch (err) {
      console.error('Error polling for new messages:', err);
    }
  }

  // Start polling for user status
  function startStatusPolling(userIds) {
    // Stop any existing polling
    stopStatusPolling();

    // Start polling
    statusPollingInterval.value = setInterval(async () => {
      try {
        await pollUserStatus(userIds);
      } catch (err) {
        console.error('Error polling for user status:', err);
      }
    }, 30000); // Poll every 30 seconds

    // Poll immediately
    pollUserStatus(userIds);

    // Clean up on component unmount
    onUnmounted(() => {
      stopStatusPolling();
    });
  }

  // Stop status polling
  function stopStatusPolling() {
    if (statusPollingInterval.value) {
      clearInterval(statusPollingInterval.value);
      statusPollingInterval.value = null;
    }
  }

  // Poll for user status
  async function pollUserStatus(userIds) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      const response = await fetch('/.netlify/functions/messages/user-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ userIds })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to poll for user status');
      }

      // Update user statuses
      userStatuses.value = { ...userStatuses.value, ...data.data.statuses };
    } catch (err) {
      console.error('Error polling for user status:', err);
    }
  }

  // Update user status (called when user opens the app)
  async function updateUserStatus(status = 'online') {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      await fetch('/.netlify/functions/messages/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ status })
      });
    } catch (err) {
      console.error('Error updating user status:', err);
    }
  }

  // Initialize WebSocket connection
  function initWebSocket() {
    // Connect to WebSocket service
    webSocketService.connect();

    // Set up WebSocket event listeners
    webSocketService.on('new-message', handleNewMessage);
    webSocketService.on('message-delivered', handleMessageDelivered);
    webSocketService.on('message-read', handleMessageRead);
    webSocketService.on('user-typing', handleUserTyping);
    webSocketService.on('user-stopped-typing', handleUserStoppedTyping);
    webSocketService.on('user-online', handleUserOnline);
    webSocketService.on('user-offline', handleUserOffline);
    webSocketService.on('fallback-to-polling', handleFallbackToPolling);

    // Update connection status
    isWebSocketConnected.value = webSocketService.getConnectionStatus().isConnected;
  }

  // Handle new message from WebSocket
  function handleNewMessage(data) {
    const { message, conversationId } = data;

    // Decrypt message content
    const conversation = conversations.value.find(c => c._id === conversationId);
    if (conversation) {
      const authStore = useAuthStore();
      message.content = encryptionService.decryptForConversation(
        message.content,
        conversation,
        authStore.user._id
      );
    }

    // Add message to current conversation if it's active
    if (currentConversation.value && currentConversation.value._id === conversationId) {
      messages.value.push(message);
    }

    // Update conversation in list
    updateConversationWithNewMessage(conversationId, message);

    // Mark message as delivered
    webSocketService.markMessageDelivered(message._id, conversationId);
  }

  // Handle message delivered status
  function handleMessageDelivered(data) {
    const { messageId, conversationId } = data;
    messageDeliveryStatus.value[messageId] = 'delivered';
  }

  // Handle message read status
  function handleMessageRead(data) {
    const { messageId, conversationId } = data;
    messageReadStatus.value[messageId] = 'read';
  }

  // Handle user typing
  function handleUserTyping(data) {
    const { userId, conversationId } = data;
    if (!typingUsers.value[conversationId]) {
      typingUsers.value[conversationId] = new Set();
    }
    typingUsers.value[conversationId].add(userId);
  }

  // Handle user stopped typing
  function handleUserStoppedTyping(data) {
    const { userId, conversationId } = data;
    if (typingUsers.value[conversationId]) {
      typingUsers.value[conversationId].delete(userId);
      if (typingUsers.value[conversationId].size === 0) {
        delete typingUsers.value[conversationId];
      }
    }
  }

  // Handle user online status
  function handleUserOnline(data) {
    const { userId } = data;
    userStatuses.value[userId] = 'online';
  }

  // Handle user offline status
  function handleUserOffline(data) {
    const { userId } = data;
    userStatuses.value[userId] = 'offline';
  }

  // Handle fallback to polling
  function handleFallbackToPolling() {
    isWebSocketConnected.value = false;
    console.log('Falling back to polling for real-time updates');
  }

  // Update conversation with new message
  function updateConversationWithNewMessage(conversationId, message) {
    const conversationIndex = conversations.value.findIndex(c => c._id === conversationId);
    if (conversationIndex !== -1) {
      const conversation = conversations.value[conversationIndex];

      // Update last message
      conversation.lastMessage = {
        _id: message._id,
        sender: message.sender,
        content: message.content,
        createdAt: message.createdAt
      };

      // Move conversation to top
      conversations.value.splice(conversationIndex, 1);
      conversations.value.unshift(conversation);
    }
  }

  // Typing indicator functions
  function startTypingIndicator() {
    if (!currentConversation.value) return;

    // Clear existing timeout
    if (typingTimeout.value) {
      clearTimeout(typingTimeout.value);
    }

    // Send typing indicator via WebSocket
    if (isWebSocketConnected.value) {
      webSocketService.sendTypingIndicator(currentConversation.value._id, true);
    }

    // Set timeout to stop typing indicator
    typingTimeout.value = setTimeout(() => {
      stopTypingIndicator();
    }, typingIndicatorDelay);
  }

  function stopTypingIndicator() {
    if (!currentConversation.value) return;

    // Clear timeout
    if (typingTimeout.value) {
      clearTimeout(typingTimeout.value);
      typingTimeout.value = null;
    }

    // Send stop typing indicator via WebSocket
    if (isWebSocketConnected.value) {
      webSocketService.sendTypingIndicator(currentConversation.value._id, false);
    }
  }

  // Get typing users for current conversation
  function getTypingUsers() {
    if (!currentConversation.value) return [];

    const typingUserIds = typingUsers.value[currentConversation.value._id];
    if (!typingUserIds || typingUserIds.size === 0) return [];

    // Convert Set to Array and filter out current user
    const authStore = useAuthStore();
    return Array.from(typingUserIds).filter(userId => userId !== authStore.user?._id);
  }

  // Mark messages as read
  function markMessagesAsRead(conversationId) {
    if (!isWebSocketConnected.value) return;

    // Find unread messages in current conversation
    const unreadMessages = messages.value.filter(
      msg => msg.conversationId === conversationId &&
             msg.sender._id !== useAuthStore().user?._id &&
             messageReadStatus.value[msg._id] !== 'read'
    );

    // Mark each message as read
    unreadMessages.forEach(message => {
      webSocketService.markMessageRead(message._id, conversationId);
    });
  }

  // Get message status
  function getMessageStatus(messageId) {
    if (messageReadStatus.value[messageId] === 'read') return 'read';
    if (messageDeliveryStatus.value[messageId] === 'delivered') return 'delivered';
    return 'sent';
  }

  // Initialize offline sync when store is created
  initOfflineSync();

  // Initialize WebSocket connection
  initWebSocket();

  return {
    // State
    conversations,
    currentConversation,
    messages,
    loading,
    error,
    page,
    hasMore,
    userStatuses,
    pendingMessages,
    isOnline,
    syncInProgress,
    typingUsers,
    messageDeliveryStatus,
    messageReadStatus,
    isWebSocketConnected,

    // Getters
    allConversations,
    currentMessages,
    conversationDetails,
    getUserStatus,
    isUserOnline,

    // Actions
    fetchConversations,
    fetchMessages,
    getOrCreateConversation,
    sendMessage,
    editMessage,
    deleteMessage,
    setCurrentConversation,
    startRealtimePolling,
    stopRealtimePolling,
    updateUserStatus,
    syncPendingMessages,
    handleOnline,
    handleOffline,
    startTypingIndicator,
    stopTypingIndicator,
    getTypingUsers,
    markMessagesAsRead,
    getMessageStatus
  };
});
