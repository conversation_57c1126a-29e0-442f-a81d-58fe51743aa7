<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useMessagesStore } from '../../stores/messages';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const route = useRoute();
const messagesStore = useMessagesStore();
const authStore = useAuthStore();

const conversationId = route.params.id;
const messageText = ref('');
const messagesContainer = ref(null);
const messageInput = ref(null);
const isTyping = ref(false);
const showEmojiPicker = ref(false);

// Computed properties
const conversation = computed(() => messagesStore.currentConversation);
const messages = computed(() => messagesStore.currentMessages);
const loading = computed(() => messagesStore.loading);
const error = computed(() => messagesStore.error);
const typingUsers = computed(() => messagesStore.getTypingUsers());
const isWebSocketConnected = computed(() => messagesStore.isWebSocketConnected);

// Get participant info
const participant = computed(() => {
  if (!conversation.value) return null;
  return conversation.value.participants?.find(p => p._id !== authStore.user?._id) ||
         conversation.value.participant;
});

const isOnline = computed(() => {
  if (!participant.value) return false;
  return messagesStore.getUserStatus(participant.value._id) === 'online';
});

// Default avatar helper
const getDefaultAvatar = () => {
  return `data:image/svg+xml,${encodeURIComponent(`
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)">
      <circle cx="50" cy="35" r="20" fill="rgba(255,255,255,0.8)"/>
      <path d="M20 80 Q20 60 50 60 Q80 60 80 80 L80 100 L20 100 Z" fill="rgba(255,255,255,0.8)"/>
    </svg>
  `)}`;
};

// Format timestamp
const formatMessageTime = (timestamp) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = (now - date) / (1000 * 60 * 60);

  if (diffInHours < 24) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } else if (diffInHours < 168) { // 7 days
    return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
  } else {
    return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
  }
};

// Check if message is from current user
const isMyMessage = (message) => {
  return message.sender._id === authStore.user?._id;
};

// Get message status icon
const getMessageStatusIcon = (message) => {
  if (!isMyMessage(message)) return '';

  const status = messagesStore.getMessageStatus(message._id);
  switch (status) {
    case 'read':
      return '✓✓';
    case 'delivered':
      return '✓✓';
    case 'sent':
      return '✓';
    default:
      return '⏳';
  }
};

// Get message status color
const getMessageStatusColor = (message) => {
  if (!isMyMessage(message)) return '';

  const status = messagesStore.getMessageStatus(message._id);
  switch (status) {
    case 'read':
      return 'text-primary';
    case 'delivered':
      return 'text-gray-400';
    case 'sent':
      return 'text-gray-500';
    default:
      return 'text-gray-600';
  }
};

// Methods
const sendMessage = async () => {
  if (!messageText.value.trim()) return;

  try {
    await messagesStore.sendMessage(messageText.value);
    messageText.value = '';

    // Stop typing indicator
    messagesStore.stopTypingIndicator();

    // Scroll to bottom
    scrollToBottom();
  } catch (err) {
    console.error('Failed to send message:', err);
  }
};

const handleKeyPress = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};

const handleInput = () => {
  // Start typing indicator
  if (!isTyping.value) {
    isTyping.value = true;
    messagesStore.startTypingIndicator();

    // Stop typing after 3 seconds of inactivity
    setTimeout(() => {
      if (isTyping.value) {
        isTyping.value = false;
        messagesStore.stopTypingIndicator();
      }
    }, 3000);
  }
};

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
};

const loadMoreMessages = async () => {
  if (loading.value || !messagesStore.hasMore) return;

  try {
    await messagesStore.fetchMessages(conversationId, false);
  } catch (err) {
    console.error('Failed to load more messages:', err);
  }
};

const handleScroll = () => {
  if (!messagesContainer.value) return;

  const { scrollTop } = messagesContainer.value;

  // Load more messages when scrolled to top
  if (scrollTop === 0 && messagesStore.hasMore && !loading.value) {
    loadMoreMessages();
  }
};

const goToProfile = (userId) => {
  // Navigate to user profile
  router.push(`/profile/${userId}`);
};

// Watch for new messages and scroll to bottom
watch(messages, () => {
  scrollToBottom();
}, { deep: true });

// Watch for conversation changes
watch(() => route.params.id, async (newId) => {
  if (newId) {
    await loadConversation(newId);
  }
});

// Load conversation
const loadConversation = async (id) => {
  try {
    // Find conversation in store or fetch it
    const conv = messagesStore.conversations.find(c => c._id === id);
    if (conv) {
      messagesStore.setCurrentConversation(conv);
    } else {
      // Fetch conversation details if not in store
      await messagesStore.fetchConversations();
      const foundConv = messagesStore.conversations.find(c => c._id === id);
      if (foundConv) {
        messagesStore.setCurrentConversation(foundConv);
      }
    }

    // Mark messages as read
    messagesStore.markMessagesAsRead(id);

    // Scroll to bottom
    scrollToBottom();
  } catch (err) {
    console.error('Failed to load conversation:', err);
  }
};

// Lifecycle hooks
onMounted(async () => {
  await loadConversation(conversationId);

  // Focus message input
  if (messageInput.value) {
    messageInput.value.focus();
  }
});

onUnmounted(() => {
  // Stop typing indicator
  messagesStore.stopTypingIndicator();

  // Clear current conversation
  messagesStore.setCurrentConversation(null);
});
</script>

<template>
  <div class="min-h-screen bg-dark-bg flex flex-col">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <!-- Participant Info -->
          <div v-if="participant" class="flex items-center cursor-pointer" @click="goToProfile(participant._id)">
            <div class="relative mr-3">
              <img
                :src="participant.profilePicture || getDefaultAvatar()"
                :alt="participant.displayName || participant.username"
                class="w-10 h-10 rounded-full object-cover"
              />
              <!-- Online indicator -->
              <div v-if="isOnline" class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-darker-bg"></div>
            </div>
            <div>
              <h1 class="text-lg font-medium">{{ participant.displayName || participant.username }}</h1>
              <div class="flex items-center text-sm text-gray-400">
                <span v-if="isOnline" class="text-green-400">Online</span>
                <span v-else>Offline</span>
                <span v-if="participant.isPremium" class="ml-2 text-yellow-400">⭐</span>
                <span v-if="participant.isVerified" class="ml-1 text-primary">✓</span>
              </div>
            </div>
          </div>

          <div v-else class="flex items-center">
            <h1 class="text-lg font-medium">Loading...</h1>
          </div>
        </div>

        <!-- Connection Status -->
        <div class="flex items-center space-x-2">
          <div v-if="isWebSocketConnected" class="flex items-center text-green-400 text-sm">
            <div class="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
            Real-time
          </div>
          <div v-else class="flex items-center text-yellow-400 text-sm">
            <div class="w-2 h-2 bg-yellow-400 rounded-full mr-1"></div>
            Polling
          </div>
        </div>
      </div>
    </header>

    <!-- Messages Container -->
    <div class="flex-1 flex flex-col pt-20 pb-20">
      <div
        ref="messagesContainer"
        @scroll="handleScroll"
        class="flex-1 overflow-y-auto px-4 py-4 space-y-4 max-w-4xl mx-auto w-full"
      >
        <!-- Loading indicator for more messages -->
        <div v-if="loading && messagesStore.hasMore" class="text-center py-4">
          <div class="inline-flex items-center text-gray-400">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading messages...
          </div>
        </div>

        <!-- Error State -->
        <div v-if="error" class="bg-red-900/20 border border-red-500/30 rounded-xl p-4 text-center">
          <p class="text-red-400">{{ error }}</p>
          <button @click="loadConversation(conversationId)" class="mt-2 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
            Retry
          </button>
        </div>

        <!-- Messages -->
        <div v-for="message in messages" :key="message._id" class="flex" :class="{ 'justify-end': isMyMessage(message) }">
          <div class="max-w-xs lg:max-w-md" :class="{ 'order-2': isMyMessage(message) }">
            <!-- Message bubble -->
            <div
              class="rounded-2xl px-4 py-2 break-words"
              :class="{
                'bg-primary text-dark-bg': isMyMessage(message),
                'bg-darker-bg text-white border border-gray-700': !isMyMessage(message)
              }"
            >
              <p class="text-sm">{{ message.content }}</p>

              <!-- Message info -->
              <div class="flex items-center justify-between mt-1 text-xs opacity-70">
                <span>{{ formatMessageTime(message.createdAt) }}</span>
                <span v-if="isMyMessage(message)" :class="getMessageStatusColor(message)">
                  {{ getMessageStatusIcon(message) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Avatar for other user's messages -->
          <div v-if="!isMyMessage(message)" class="order-1 mr-2 mt-auto">
            <img
              :src="participant?.profilePicture || getDefaultAvatar()"
              :alt="participant?.displayName || participant?.username"
              class="w-8 h-8 rounded-full object-cover cursor-pointer"
              @click="goToProfile(message.sender._id)"
            />
          </div>
        </div>

        <!-- Typing indicator -->
        <div v-if="typingUsers.length > 0" class="flex items-center space-x-2 text-gray-400 text-sm">
          <div class="flex space-x-1">
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
          </div>
          <span>{{ participant?.displayName || participant?.username }} is typing...</span>
        </div>
      </div>
    </div>

    <!-- Message Input -->
    <div class="fixed bottom-0 left-0 right-0 bg-darker-bg/80 backdrop-blur-md border-t border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3">
        <div class="flex items-end space-x-3">
          <!-- Message Input -->
          <div class="flex-1 relative">
            <textarea
              ref="messageInput"
              v-model="messageText"
              @keypress="handleKeyPress"
              @input="handleInput"
              placeholder="Type a message..."
              rows="1"
              class="w-full bg-dark-bg border border-gray-700 rounded-2xl px-4 py-3 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-primary transition-colors resize-none max-h-32"
              style="min-height: 44px;"
            ></textarea>

            <!-- Emoji button -->
            <button
              @click="showEmojiPicker = !showEmojiPicker"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
          </div>

          <!-- Send Button -->
          <button
            @click="sendMessage"
            :disabled="!messageText.trim() || loading"
            class="bg-primary text-dark-bg p-3 rounded-full hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="loading" class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>

        <!-- Quick emoji reactions -->
        <div v-if="showEmojiPicker" class="mt-3 p-3 bg-dark-bg rounded-xl border border-gray-700">
          <div class="flex flex-wrap gap-2">
            <button
              v-for="emoji in ['👍', '❤️', '😂', '😮', '😢', '😡', '👏', '🔥', '💯', '🎉']"
              :key="emoji"
              @click="messageText += emoji; showEmojiPicker = false"
              class="text-2xl hover:bg-gray-700 rounded-lg p-2 transition-colors"
            >
              {{ emoji }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
