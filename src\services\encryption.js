import CryptoJS from 'crypto-js';

class EncryptionService {
  constructor() {
    // Use a combination of user-specific and app-specific keys
    this.appSecret = 'NeTuArk-2024-Secure-Messaging';
  }

  // Generate encryption key based on conversation participants
  generateConversationKey(userId1, userId2) {
    // Sort user IDs to ensure consistent key generation regardless of order
    const sortedIds = [userId1, userId2].sort();
    const keyMaterial = `${this.appSecret}-${sortedIds[0]}-${sortedIds[1]}`;
    
    // Generate a SHA-256 hash to use as encryption key
    return CryptoJS.SHA256(keyMaterial).toString();
  }

  // Generate encryption key for group conversations
  generateGroupKey(conversationId, participantIds) {
    const sortedIds = [...participantIds].sort();
    const keyMaterial = `${this.appSecret}-group-${conversationId}-${sortedIds.join('-')}`;
    
    return CryptoJS.SHA256(keyMaterial).toString();
  }

  // Encrypt message content
  encryptMessage(content, conversationKey) {
    try {
      // Generate a random IV for each message
      const iv = CryptoJS.lib.WordArray.random(16);
      
      // Encrypt the content
      const encrypted = CryptoJS.AES.encrypt(content, conversationKey, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      // Combine IV and encrypted content
      const encryptedData = {
        iv: iv.toString(),
        content: encrypted.toString()
      };

      return JSON.stringify(encryptedData);
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt message');
    }
  }

  // Decrypt message content
  decryptMessage(encryptedContent, conversationKey) {
    try {
      // Parse the encrypted data
      const encryptedData = JSON.parse(encryptedContent);
      const iv = CryptoJS.enc.Hex.parse(encryptedData.iv);

      // Decrypt the content
      const decrypted = CryptoJS.AES.decrypt(encryptedData.content, conversationKey, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('Decryption error:', error);
      // Return original content if decryption fails (for backward compatibility)
      return encryptedContent;
    }
  }

  // Check if content is encrypted
  isEncrypted(content) {
    try {
      const parsed = JSON.parse(content);
      return parsed.iv && parsed.content;
    } catch {
      return false;
    }
  }

  // Encrypt message for storage/transmission
  encryptForConversation(content, conversation, currentUserId) {
    try {
      let conversationKey;

      if (conversation.participants.length === 2) {
        // 1-on-1 conversation
        const otherParticipant = conversation.participants.find(p => p.toString() !== currentUserId);
        conversationKey = this.generateConversationKey(currentUserId, otherParticipant.toString());
      } else {
        // Group conversation
        conversationKey = this.generateGroupKey(
          conversation._id.toString(),
          conversation.participants.map(p => p.toString())
        );
      }

      return this.encryptMessage(content, conversationKey);
    } catch (error) {
      console.error('Error encrypting message for conversation:', error);
      // Return original content if encryption fails
      return content;
    }
  }

  // Decrypt message from storage/transmission
  decryptForConversation(encryptedContent, conversation, currentUserId) {
    try {
      // Check if content is actually encrypted
      if (!this.isEncrypted(encryptedContent)) {
        return encryptedContent;
      }

      let conversationKey;

      if (conversation.participants.length === 2) {
        // 1-on-1 conversation
        const otherParticipant = conversation.participants.find(p => p.toString() !== currentUserId);
        conversationKey = this.generateConversationKey(currentUserId, otherParticipant.toString());
      } else {
        // Group conversation
        conversationKey = this.generateGroupKey(
          conversation._id.toString(),
          conversation.participants.map(p => p.toString())
        );
      }

      return this.decryptMessage(encryptedContent, conversationKey);
    } catch (error) {
      console.error('Error decrypting message for conversation:', error);
      // Return original content if decryption fails
      return encryptedContent;
    }
  }

  // Batch decrypt messages
  decryptMessages(messages, conversation, currentUserId) {
    return messages.map(message => ({
      ...message,
      content: this.decryptForConversation(message.content, conversation, currentUserId)
    }));
  }

  // Generate secure hash for message integrity
  generateMessageHash(content, timestamp, senderId) {
    const hashInput = `${content}-${timestamp}-${senderId}`;
    return CryptoJS.SHA256(hashInput).toString();
  }

  // Verify message integrity
  verifyMessageIntegrity(content, timestamp, senderId, hash) {
    const expectedHash = this.generateMessageHash(content, timestamp, senderId);
    return expectedHash === hash;
  }

  // Generate secure random string for message IDs
  generateSecureId(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * chars.length);
      result += chars[randomIndex];
    }
    
    return result;
  }

  // Sanitize message content to prevent XSS
  sanitizeContent(content) {
    return content
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  // Validate message content
  validateMessageContent(content) {
    if (!content || typeof content !== 'string') {
      throw new Error('Message content must be a non-empty string');
    }

    if (content.length > 10000) {
      throw new Error('Message content is too long (max 10,000 characters)');
    }

    // Check for potentially malicious content
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe/i,
      /<object/i,
      /<embed/i
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(content)) {
        throw new Error('Message content contains potentially malicious code');
      }
    }

    return true;
  }
}

// Create singleton instance
const encryptionService = new EncryptionService();

export default encryptionService;
