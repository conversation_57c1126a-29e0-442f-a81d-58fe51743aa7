// Optional development WebSocket server for testing
// This is only for local development - not needed for production

const { createServer } = require('http');
const { Server } = require('socket.io');

const httpServer = createServer();
const io = new Server(httpServer, {
  cors: {
    origin: "http://localhost:5173",
    methods: ["GET", "POST"]
  }
});

// Store connected users
const connectedUsers = new Map();

io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Handle user authentication
  socket.on('join-user-room', (userId) => {
    socket.userId = userId;
    socket.join(`user-${userId}`);
    connectedUsers.set(userId, socket.id);
    console.log(`User ${userId} joined their room`);
  });

  // Handle joining conversation rooms
  socket.on('join-conversation', (conversationId) => {
    socket.join(`conversation-${conversationId}`);
    console.log(`User ${socket.userId} joined conversation ${conversationId}`);
  });

  // Handle leaving conversation rooms
  socket.on('leave-conversation', (conversationId) => {
    socket.leave(`conversation-${conversationId}`);
    console.log(`User ${socket.userId} left conversation ${conversationId}`);
  });

  // Handle sending messages
  socket.on('send-message', (data) => {
    const { conversationId, content } = data;
    
    // Broadcast to all users in the conversation
    socket.to(`conversation-${conversationId}`).emit('new-message', {
      message: {
        _id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        conversationId,
        sender: { _id: socket.userId },
        content,
        createdAt: new Date().toISOString()
      },
      conversationId
    });

    console.log(`Message sent in conversation ${conversationId}`);
  });

  // Handle typing indicators
  socket.on('start-typing', (data) => {
    const { conversationId } = data;
    socket.to(`conversation-${conversationId}`).emit('user-typing', {
      userId: socket.userId,
      conversationId
    });
  });

  socket.on('stop-typing', (data) => {
    const { conversationId } = data;
    socket.to(`conversation-${conversationId}`).emit('user-stopped-typing', {
      userId: socket.userId,
      conversationId
    });
  });

  // Handle message delivery status
  socket.on('message-delivered', (data) => {
    const { messageId, conversationId } = data;
    socket.to(`conversation-${conversationId}`).emit('message-delivered', {
      messageId,
      conversationId
    });
  });

  socket.on('message-read', (data) => {
    const { messageId, conversationId } = data;
    socket.to(`conversation-${conversationId}`).emit('message-read', {
      messageId,
      conversationId
    });
  });

  // Handle user status updates
  socket.on('update-status', (data) => {
    const { status } = data;
    
    // Broadcast to all connected users
    socket.broadcast.emit('user-online', {
      userId: socket.userId,
      status
    });
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
    
    if (socket.userId) {
      connectedUsers.delete(socket.userId);
      
      // Broadcast offline status
      socket.broadcast.emit('user-offline', {
        userId: socket.userId
      });
    }
  });
});

const PORT = process.env.WS_PORT || 3001;

httpServer.listen(PORT, () => {
  console.log(`WebSocket server running on port ${PORT}`);
  console.log('This is for development only - production uses polling fallback');
});
