<template>
  <div class="min-h-screen bg-dark-bg text-white p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-8 text-center">NeTuArk Messages System Test</h1>
      
      <!-- Test Results -->
      <div class="grid gap-6">
        <!-- API Connection Test -->
        <div class="bg-darker-bg rounded-xl p-6 border border-gray-800">
          <h2 class="text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-server mr-2"></i>
            API Connection Test
          </h2>
          <button 
            @click="testAPIConnection" 
            :disabled="testing.api"
            class="bg-primary hover:bg-primary-dark px-4 py-2 rounded-lg disabled:opacity-50 mb-4"
          >
            {{ testing.api ? 'Testing...' : 'Test API Connection' }}
          </button>
          <div v-if="results.api" class="mt-4">
            <div :class="results.api.success ? 'text-green-400' : 'text-red-400'" class="font-medium">
              {{ results.api.success ? '✓ Success' : '✗ Failed' }}
            </div>
            <pre class="bg-gray-900 p-3 rounded mt-2 text-sm overflow-auto">{{ JSON.stringify(results.api.data, null, 2) }}</pre>
          </div>
        </div>

        <!-- Database Connection Test -->
        <div class="bg-darker-bg rounded-xl p-6 border border-gray-800">
          <h2 class="text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-database mr-2"></i>
            Database Connection Test
          </h2>
          <button 
            @click="testDatabaseConnection" 
            :disabled="testing.database"
            class="bg-primary hover:bg-primary-dark px-4 py-2 rounded-lg disabled:opacity-50 mb-4"
          >
            {{ testing.database ? 'Testing...' : 'Test Database Connection' }}
          </button>
          <div v-if="results.database" class="mt-4">
            <div :class="results.database.success ? 'text-green-400' : 'text-red-400'" class="font-medium">
              {{ results.database.success ? '✓ Success' : '✗ Failed' }}
            </div>
            <pre class="bg-gray-900 p-3 rounded mt-2 text-sm overflow-auto">{{ JSON.stringify(results.database.data, null, 2) }}</pre>
          </div>
        </div>

        <!-- Messages API Test -->
        <div class="bg-darker-bg rounded-xl p-6 border border-gray-800">
          <h2 class="text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-comments mr-2"></i>
            Messages API Test
          </h2>
          <button 
            @click="testMessagesAPI" 
            :disabled="testing.messages"
            class="bg-primary hover:bg-primary-dark px-4 py-2 rounded-lg disabled:opacity-50 mb-4"
          >
            {{ testing.messages ? 'Testing...' : 'Test Messages API' }}
          </button>
          <div v-if="results.messages" class="mt-4">
            <div :class="results.messages.success ? 'text-green-400' : 'text-red-400'" class="font-medium">
              {{ results.messages.success ? '✓ Success' : '✗ Failed' }}
            </div>
            <pre class="bg-gray-900 p-3 rounded mt-2 text-sm overflow-auto">{{ JSON.stringify(results.messages.data, null, 2) }}</pre>
          </div>
        </div>

        <!-- Authentication Test -->
        <div class="bg-darker-bg rounded-xl p-6 border border-gray-800">
          <h2 class="text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-user-shield mr-2"></i>
            Authentication Test
          </h2>
          <div class="mb-4">
            <div :class="authStore.isAuthenticated ? 'text-green-400' : 'text-red-400'" class="font-medium">
              {{ authStore.isAuthenticated ? '✓ Authenticated' : '✗ Not Authenticated' }}
            </div>
            <div v-if="authStore.user" class="text-sm text-gray-400 mt-2">
              User: {{ authStore.user.username }} ({{ authStore.user.email }})
            </div>
          </div>
        </div>

        <!-- Environment Info -->
        <div class="bg-darker-bg rounded-xl p-6 border border-gray-800">
          <h2 class="text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-info-circle mr-2"></i>
            Environment Information
          </h2>
          <div class="space-y-2 text-sm">
            <div><strong>Current URL:</strong> {{ currentUrl }}</div>
            <div><strong>User Agent:</strong> {{ navigator.userAgent }}</div>
            <div><strong>Online Status:</strong> {{ navigator.onLine ? 'Online' : 'Offline' }}</div>
            <div><strong>Local Storage Available:</strong> {{ localStorageAvailable ? 'Yes' : 'No' }}</div>
          </div>
        </div>
      </div>

      <!-- Back to Messages -->
      <div class="mt-8 text-center">
        <router-link 
          to="/messages" 
          class="bg-gray-700 hover:bg-gray-600 px-6 py-3 rounded-lg inline-flex items-center"
        >
          <i class="fas fa-arrow-left mr-2"></i>
          Back to Messages
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '../../stores/auth';

const authStore = useAuthStore();

// Test states
const testing = ref({
  api: false,
  database: false,
  messages: false
});

const results = ref({
  api: null,
  database: null,
  messages: null
});

// Environment checks
const currentUrl = computed(() => window.location.href);

const localStorageAvailable = computed(() => {
  try {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
    return true;
  } catch {
    return false;
  }
});

// Test functions
async function testAPIConnection() {
  testing.value.api = true;
  try {
    const response = await fetch('/.netlify/functions/messages/test');
    const data = await response.json();
    
    results.value.api = {
      success: response.ok,
      status: response.status,
      data
    };
  } catch (error) {
    results.value.api = {
      success: false,
      error: error.message,
      data: { error: error.message }
    };
  } finally {
    testing.value.api = false;
  }
}

async function testDatabaseConnection() {
  testing.value.database = true;
  try {
    const response = await fetch('/.netlify/functions/db-status');
    const data = await response.json();
    
    results.value.database = {
      success: response.ok,
      status: response.status,
      data
    };
  } catch (error) {
    results.value.database = {
      success: false,
      error: error.message,
      data: { error: error.message }
    };
  } finally {
    testing.value.database = false;
  }
}

async function testMessagesAPI() {
  testing.value.messages = true;
  try {
    if (!authStore.token) {
      throw new Error('Not authenticated');
    }

    const response = await fetch('/.netlify/functions/messages/conversations', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });
    const data = await response.json();
    
    results.value.messages = {
      success: response.ok,
      status: response.status,
      data
    };
  } catch (error) {
    results.value.messages = {
      success: false,
      error: error.message,
      data: { error: error.message }
    };
  } finally {
    testing.value.messages = false;
  }
}

onMounted(() => {
  // Auto-run tests on mount
  setTimeout(() => {
    testAPIConnection();
    testDatabaseConnection();
    if (authStore.isAuthenticated) {
      testMessagesAPI();
    }
  }, 1000);
});
</script>

<style scoped>
.bg-dark-bg {
  background-color: #0a0a0a;
}

.bg-darker-bg {
  background-color: #1a1a1a;
}

.bg-primary {
  background-color: #00d4ff;
}

.bg-primary-dark {
  background-color: #00b8e6;
}

.text-primary {
  color: #00d4ff;
}
</style>
