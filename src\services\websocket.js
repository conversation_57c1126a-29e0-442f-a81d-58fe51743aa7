import { io } from 'socket.io-client';
import { useAuthStore } from '../stores/auth';

class WebSocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.listeners = new Map();
    this.messageQueue = [];
  }

  // Initialize WebSocket connection
  connect() {
    const authStore = useAuthStore();

    if (!authStore.token) {
      console.warn('No auth token available for WebSocket connection');
      this.fallbackToPolling();
      return;
    }

    // For production deployment on Netlify, skip WebSocket and use polling
    if (import.meta.env.PROD) {
      console.log('Production environment detected, using polling instead of WebSocket');
      this.fallbackToPolling();
      return;
    }

    // Only try WebSocket in development
    const wsUrl = this.getWebSocketUrl();
    if (!wsUrl) {
      console.log('No WebSocket URL available, falling back to polling');
      this.fallbackToPolling();
      return;
    }

    try {
      // Try to connect to WebSocket server (development only)
      this.socket = io(wsUrl, {
        auth: {
          token: authStore.token
        },
        transports: ['websocket', 'polling'],
        timeout: 3000,
        reconnection: false, // Disable reconnection to avoid spam
        autoConnect: true
      });

      this.setupEventListeners();
    } catch (error) {
      console.warn('WebSocket connection failed, falling back to polling:', error);
      this.fallbackToPolling();
    }
  }

  // Get WebSocket URL based on environment
  getWebSocketUrl() {
    // Only return WebSocket URL in development
    if (import.meta.env.DEV) {
      return 'http://localhost:3001'; // Local development WebSocket server
    }

    // For production, return null to force polling
    return null;
  }

  // Setup WebSocket event listeners
  setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Send queued messages
      this.processMessageQueue();
      
      // Join user room for personal notifications
      const authStore = useAuthStore();
      if (authStore.user) {
        this.socket.emit('join-user-room', authStore.user._id);
      }
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.isConnected = false;
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.socket.connect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.warn('WebSocket connection error, falling back to polling:', error);
      this.fallbackToPolling();
    });

    // Message events
    this.socket.on('new-message', (data) => {
      this.emit('new-message', data);
    });

    this.socket.on('message-delivered', (data) => {
      this.emit('message-delivered', data);
    });

    this.socket.on('message-read', (data) => {
      this.emit('message-read', data);
    });

    this.socket.on('user-typing', (data) => {
      this.emit('user-typing', data);
    });

    this.socket.on('user-stopped-typing', (data) => {
      this.emit('user-stopped-typing', data);
    });

    this.socket.on('user-online', (data) => {
      this.emit('user-online', data);
    });

    this.socket.on('user-offline', (data) => {
      this.emit('user-offline', data);
    });
  }

  // Fallback to polling when WebSocket is not available
  fallbackToPolling() {
    console.log('Using polling fallback for real-time updates');
    this.isConnected = false;
    
    // The existing polling mechanism in the messages store will handle this
    this.emit('fallback-to-polling');
  }

  // Send message through WebSocket
  sendMessage(conversationId, content) {
    if (this.isConnected && this.socket) {
      this.socket.emit('send-message', {
        conversationId,
        content,
        timestamp: new Date().toISOString()
      });
    } else {
      // Queue message for later sending
      this.messageQueue.push({
        type: 'send-message',
        data: { conversationId, content, timestamp: new Date().toISOString() }
      });
    }
  }

  // Mark message as delivered
  markMessageDelivered(messageId, conversationId) {
    if (this.isConnected && this.socket) {
      this.socket.emit('message-delivered', {
        messageId,
        conversationId,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Mark message as read
  markMessageRead(messageId, conversationId) {
    if (this.isConnected && this.socket) {
      this.socket.emit('message-read', {
        messageId,
        conversationId,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Send typing indicator
  sendTypingIndicator(conversationId, isTyping) {
    if (this.isConnected && this.socket) {
      this.socket.emit(isTyping ? 'start-typing' : 'stop-typing', {
        conversationId,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Join conversation room
  joinConversation(conversationId) {
    if (this.isConnected && this.socket) {
      this.socket.emit('join-conversation', conversationId);
    }
  }

  // Leave conversation room
  leaveConversation(conversationId) {
    if (this.isConnected && this.socket) {
      this.socket.emit('leave-conversation', conversationId);
    }
  }

  // Update user online status
  updateOnlineStatus(status = 'online') {
    if (this.isConnected && this.socket) {
      this.socket.emit('update-status', {
        status,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Process queued messages
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const { type, data } = this.messageQueue.shift();
      
      if (this.socket) {
        this.socket.emit(type, data);
      }
    }
  }

  // Event listener management
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in WebSocket event callback:', error);
        }
      });
    }
  }

  // Disconnect WebSocket
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.listeners.clear();
    this.messageQueue = [];
  }

  // Get connection status
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      hasSocket: !!this.socket
    };
  }
}

// Create singleton instance
const webSocketService = new WebSocketService();

export default webSocketService;
